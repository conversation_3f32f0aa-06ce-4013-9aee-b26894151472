# 权益领取接口文档

## 接口概述
用户在优酷app券包中点击领取链接，跳转到零世纪页面领取天猫商城权益卡。每隔5天可领取1张，共6张。零世纪只负责权益卡的生成和领取，不涉及核销流程。

## 接口信息
- **接口名称**: 权益领取
- **请求方式**: GET
- **接口地址**: `https://api.lingshiji.com/youku/benefit/claim`

## 请求参数

### URL参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| token | String | 是 | 用户身份令牌，由优酷生成 |
| mobile | String | 是 | 用户手机号 |
| orderId | String | 是 | 优酷订单号 |
| timestamp | Long | 是 | 毫秒时间戳 |
| sign | String | 是 | 签名验证 |

### 请求示例
```
GET https://api.lingshiji.com/youku/benefit/claim?token=abc123&mobile=13534838490&orderId=YK202401010001&timestamp=1714463976291&sign=55e2f690df3f9106dfe456eb1944665d
```

## 返回参数

### 页面响应
成功时返回HTML页面，包含：
- 权益卡信息展示
- 领取按钮
- 领取状态提示
- 下次可领取时间

### API响应（AJAX请求）
| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| code | String | 状态码 |
| message | String | 状态描述 |
| data | Object | 业务数据 |

### 业务数据 (data)
| 参数名 | 类型 | 说明 |
|--------|------|------|
| mobile | String | 用户手机号 |
| orderId | String | 优酷订单号 |
| totalCount | Integer | 总权益卡数量（6张） |
| claimedCount | Integer | 已领取数量 |
| canClaim | Boolean | 当前是否可以领取 |
| nextClaimTime | Long | 下次可领取时间戳 |
| benefitCard | Object | 权益卡信息 |

### 权益卡信息 (benefitCard)
| 参数名 | 类型 | 说明 |
|--------|------|------|
| cardId | String | 权益卡ID |
| cardType | String | 权益卡类型 |
| amount | Integer | 权益金额（分） |
| validDays | Integer | 有效天数 |
| description | String | 权益描述 |
| cardCode | String | 权益卡兑换码 |
| status | String | 权益卡状态：ACTIVE-有效，EXPIRED-已过期 |

### 成功响应示例
```json
{
  "success": true,
  "code": "SUCCESS",
  "message": "领取成功",
  "data": {
    "mobile": "13534838490",
    "orderId": "YK202401010001",
    "totalCount": 6,
    "claimedCount": 1,
    "canClaim": false,
    "nextClaimTime": 1714895976291,
    "benefitCard": {
      "cardId": "TMC202401010001",
      "cardType": "TMALL_COUPON",
      "amount": 500,
      "validDays": 30,
      "description": "5元天猫商城权益卡",
      "cardCode": "TMC20240101001",
      "status": "ACTIVE"
    }
  }
}
```

### 不可领取响应示例
```json
{
  "success": true,
  "code": "CANNOT_CLAIM",
  "message": "距离下次领取还需要3天",
  "data": {
    "mobile": "13534838490",
    "orderId": "YK202401010001", 
    "totalCount": 6,
    "claimedCount": 2,
    "canClaim": false,
    "nextClaimTime": 1714895976291,
    "benefitCard": null
  }
}
```

## 领取规则
1. 用户购买成功后，可立即领取第1张权益卡
2. 后续每隔5天可领取1张，共6张
3. 权益卡有效期为30天
4. 已领取的权益卡不可重复领取

## 状态码说明
| 状态码 | 说明 |
|--------|------|
| SUCCESS | 领取成功 |
| CANNOT_CLAIM | 当前不可领取 |
| ALL_CLAIMED | 已领取完所有权益卡 |
| INVALID_TOKEN | 无效的令牌 |
| INVALID_ORDER | 订单不存在或无效 |
| INVALID_SIGN | 签名验证失败 |
| SYSTEM_ERROR | 系统错误 |

## 签名算法
使用HMAC-SHA256算法，签名字符串格式：
```
mobile=13534838490&orderId=YK202401010001&timestamp=1714463976291&token=abc123
```

参数按ASCII码升序排列，使用&连接，最后使用密钥进行HMAC-SHA256加密。
