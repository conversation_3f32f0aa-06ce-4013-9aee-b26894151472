# 优酷开放接口接入-三方订单状态查询

## 接入指南

### 优酷提供信息

- **合作方渠道标示** `yk23partappkey`，放在header里
- **合作方密钥** `yk23partsecret`，用于HMAC_MD5加密计算签名
- 具体接口里需要的其它参数
  - `biz` 渠道订单标识

### 接口信息

- **地址**: `https://3part.youku.com/common/proxy/v1`
- **请求方式**: POST
- **content-type**: `application/json`

#### 请求头header里通用参数

| 参数 | 类型 | 说明 |
|------|------|------|
| yk23partappkey | String | 合作方渠道标示 |
| yk23partsign | String | 签名 |
| timestamp | Long | 毫秒时间戳，签名默认有30分钟有效期限制（注意：该字段参与生成签名） |

## 生成签名方法

### 签名算法
**HMAC_MD5**

### 签名字符串
时间戳和请求参数按照key的字符ASCII码升序排列。参数为对象的，对象内参数也要按ASCII码升序排列。

**举例**: post请求body体 `{"a":"v1", "b":"v2", "z":"v3"}`，header里的时间戳timestamp为1685441869000。

则参与计算签名的字符串为: `a=v1&b=v2&timestamp=1685441869000&z=v3`

### 验证示例

- **密钥yk23partsecret**: test
- **timestamp**: 1714463976291
- **请求参数**:
```json
{
  "apiName": "youku.isp.nc1.checkToken",
  "biz": "cmcc_nc1_jiangsu",
  "source": "checkToken",
  "bizParam": {
    "token": "abc"
  }
}
```

**计算签名的字符串**:
```
apiName=youku.isp.nc1.checkToken&biz=cmcc_nc1_jiangsu&bizParam={"token":"abc"}&source=checkToken&timestamp=1720609398057
```

**得到签名sign**: `55e2f690df3f9106dfe456eb1944665d`

**验证网站**: http://www.jsons.cn/allencrypt/

## 签名代码示例

```java
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class SignUtil {
    //签名算法
    public static final String HMAC_MD5 = "HmacMD5";
    protected final static String NAME_VALUE_SP = "=";
    protected final static String GROUP_SP = "&";
    
    /**
     * 生成签名数据_HmacSHA1加密
     *
     * @param data 待加密的数据
     * @param key 加密使用的key
     * @throws NoSuchAlgorithmException
     */
    public static String getSignature(String data, String key, String cryptoType) throws Exception {
        byte[] keyBytes = key.getBytes();
        // 根据给定的字节数组构造一个密钥。
        SecretKeySpec signingKey = new SecretKeySpec(keyBytes, cryptoType);
        Mac mac = Mac.getInstance(cryptoType);
        mac.init(signingKey);
        byte[] rawHmac = mac.doFinal(data.getBytes());
        String hexBytes = byte2hex(rawHmac);
        return hexBytes;
    }
    
    private static String byte2hex(final byte[] b) {
        String hs = "";
        String stmp = "";
        for (int n = 0; n < b.length; n++) {
            // 以十六进制（基数 16）无符号整数形式返回一个整数参数的字符串表示形式。
            stmp = (Integer.toHexString(b[n] & 0xFF));
            if (stmp.length() == 1) {
                hs = hs + "0" + stmp;
            } else {
                hs = hs + stmp;
            }
        }
        return hs;
    }
    
    /**
     * @param params 参数
     * @param secret 秘钥
     * @return
     * @throws Exception
     */
    public static String createSign(Map<String, String> params, String secret, String cryptoType) throws Exception {
        String data = castParams2String(params);
        String signature = getSignature(data, secret, cryptoType);
        return signature;
    }
    
    /**
     * map ->string 参数字典排序
     *
     * @param params
     * @return
     */
    public static String castParams2String(Map<String, String> params) {
        StringBuilder stringBuilder = new StringBuilder();
        ArrayList<String> keyList = new ArrayList<>(params.keySet());
        Collections.sort(keyList);
        for (int i = 0; i < keyList.size(); i++) {
            String key = keyList.get(i);
            if (i == keyList.size() - 1) {
                stringBuilder.append(key).append(NAME_VALUE_SP).append(String.valueOf(params.getOrDefault(key, "")));
            } else {
                stringBuilder.append(key).append(NAME_VALUE_SP).append(String.valueOf(params.getOrDefault(key, ""))).append(GROUP_SP);
            }
        }
        return stringBuilder.toString();
    }
    
    public static void main(String[] args) throws Exception {
        String timestamp = String.valueOf(System.currentTimeMillis());
        Map<String, String> data = new HashMap<>();
        data.put("apiName", "xxx");
        data.put("biz", "xxx");
        data.put("xxx", "xxx");
        
        Map<String, String> signParam = new HashMap<>(data);
        signParam.put("timestamp", timestamp);
        
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("yk23partappkey", "***");
        headers.put("timestamp", timestamp);
        headers.put("yk23partsign", createSign(signParam, "***", HMAC_MD5));
        
        String res = HttpUtil.postRequest("https://3part.youku.com/common/proxy/v1", JSON.toJSONString(data), headers);
        System.out.println(res);
    }
}
```

## 开放接口

### 查询订单状态

#### 请求体包含字段

| 参数 | 类型 | 是否必填 | 说明 |
|------|------|----------|------|
| apiName | String | 是 | 接口标识，查询订单状态(固定值): `youku.union.order.query` |
| biz | String | 是 | 渠道订单标识 |
| orderId | String | 是 | 订单id |

#### 示例

```json
{
  "apiName": "youku.union.order.query",
  "biz": "***",
  "orderId":"***"
}
```

#### 返回

##### 公共字段

| 业务字段 | 父节点 | 类型 | 备注 |
|----------|--------|------|------|
| success | 无 | boolean | 开放接口请求是否成功 |
| msgCode | 无 | String | 开放接口状态码 |
| msgInfo | 无 | String | 开放接口状态码描述 |
| content | 无 | Object | 业务返回结果 |

##### 业务字段

| 业务字段 | 父节点 | 类型 | 备注 |
|----------|--------|------|------|
| success | content | boolean | 是否成功 |
| code | content | String | 业务状态码 |
| msg | content | String | 业务状态码描述 |
| data | content | Object | 业务数据 |
| orderId | data | String | 优酷外部订单号 |
| isRefund | data | boolean | 是否退款 |
| refundTime | data | Date | 退款时间 |
| cycleBuyType | data | String | 连续包订单类型 sign-签约,renew-续费 |
| unsignedState | data | String | 解约/冻结状态 UNSIGNED-解约, SIGNED_NO_OP- 冻结 |
| unsignedTime | data | Date | 解约/冻结状态更新时间 |

#### 示例

会有些冗余字段返回，可忽略，按上面描述的返回字段获取接口请求结果和业务结果即可。

```json
{
  "content": {
    "msg": null,
    "errorMap": null,
    "code": null,
    "data": {
      "cycleBuyType": "renew",
      "unsignedState": null,
      "orderId": "***",
      "isRefund": true,
      "refundTime": 1736826261000,
      "unsignedTime": null
    },
    "description": null,
    "apiResultMsg": null,
    "succ": 1,
    "success": true,
    "apiResultStatus": true,
    "apiResultCode": null,
    "class": "com.youku.crmtoali.client.common.ResponseDTO",
    "msgInfo": null,
    "msgCode": null
  },
  "success": true,
  "code": "SUCCESS",
  "msgCode": "SUCCESS",
  "message": null,
  "msgInfo": null,
  "description": null,
  "currentTime": null,
  "apiResultStatus": true,
  "apiResultCode": "SUCCESS",
  "apiResultMsg": null
}
```

## 状态码

### 开放接口请求状态码

| 状态码 | 描述 |
|--------|------|
| SUCCESS | 成功 |
| FAIL | 失败 |

### 业务状态码

| 状态码 | 描述 |
|--------|------|
| SUCCESS | 成功 |
| BIZ_ERROR | 业务异常，具体原因见异常描述 |
| FAIL_SERVICE_EXCEPTION | 服务未知异常 |
| APPTYPE_NOT_MATCH | 订单渠道校验失败 |
| FAIL_NO_PERMISSION | 未授权 |