# 权益卡状态查询接口文档

## 接口概述
提供权益卡状态查询功能，用户可以查看权益卡信息、有效期和兑换码。零世纪只负责权益卡的生成、发放和状态管理，不涉及核销流程。

## 接口信息
- **接口名称**: 权益卡状态查询
- **请求方式**: GET
- **接口地址**: `https://api.lingshiji.com/youku/benefit/status`

## 请求参数

### URL参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mobile | String | 是 | 用户手机号 |
| orderId | String | 是 | 优酷订单号 |
| timestamp | Long | 是 | 毫秒时间戳 |
| sign | String | 是 | 签名验证 |

### 请求示例
```
GET https://api.lingshiji.com/youku/benefit/status?mobile=13534838490&orderId=YK202401010001&timestamp=1714463976291&sign=55e2f690df3f9106dfe456eb1944665d
```

## 返回参数

### 响应体结构
| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| code | String | 状态码 |
| message | String | 状态描述 |
| data | Object | 业务数据 |

### 业务数据 (data)
| 参数名 | 类型 | 说明 |
|--------|------|------|
| mobile | String | 用户手机号 |
| orderId | String | 优酷订单号 |
| totalCount | Integer | 总权益卡数量（6张） |
| claimedCount | Integer | 已领取数量 |
| availableCount | Integer | 可用权益卡数量 |
| expiredCount | Integer | 已过期权益卡数量 |
| benefitCards | Array | 权益卡列表 |

### 权益卡信息 (benefitCards)
| 参数名 | 类型 | 说明 |
|--------|------|------|
| cardId | String | 权益卡ID |
| cardType | String | 权益卡类型 |
| amount | Integer | 权益金额（分） |
| validDays | Integer | 有效天数 |
| description | String | 权益描述 |
| cardCode | String | 权益卡兑换码 |
| status | String | 权益卡状态：PENDING-待领取，ACTIVE-有效，EXPIRED-已过期 |
| claimTime | Long | 领取时间戳 |
| expireTime | Long | 过期时间戳 |
| canClaimTime | Long | 可领取时间戳 |

### 成功响应示例
```json
{
  "success": true,
  "code": "SUCCESS",
  "message": "查询成功",
  "data": {
    "mobile": "13534838490",
    "orderId": "YK202401010001",
    "totalCount": 6,
    "claimedCount": 2,
    "availableCount": 1,
    "expiredCount": 1,
    "benefitCards": [
      {
        "cardId": "TMC202401010001",
        "cardType": "TMALL_COUPON",
        "amount": 500,
        "validDays": 30,
        "description": "5元天猫商城权益卡",
        "cardCode": "TMC20240101001",
        "status": "EXPIRED",
        "claimTime": 1714463976291,
        "expireTime": 1717055976291,
        "canClaimTime": 1714463976291
      },
      {
        "cardId": "TMC202401010002",
        "cardType": "TMALL_COUPON",
        "amount": 500,
        "validDays": 30,
        "description": "5元天猫商城权益卡",
        "cardCode": "TMC20240101002",
        "status": "ACTIVE",
        "claimTime": 1714895976291,
        "expireTime": 1717487976291,
        "canClaimTime": 1714895976291
      },
      {
        "cardId": "TMC202401010003",
        "cardType": "TMALL_COUPON",
        "amount": 500,
        "validDays": 30,
        "description": "5元天猫商城权益卡",
        "cardCode": null,
        "status": "PENDING",
        "claimTime": null,
        "expireTime": null,
        "canClaimTime": 1715327976291
      }
    ]
  }
}
```

### 错误响应示例
```json
{
  "success": false,
  "code": "ORDER_NOT_FOUND",
  "message": "订单不存在",
  "data": null
}
```

## 状态码说明
| 状态码 | 说明 |
|--------|------|
| SUCCESS | 查询成功 |
| ORDER_NOT_FOUND | 订单不存在 |
| INVALID_MOBILE | 手机号不匹配 |
| INVALID_SIGN | 签名验证失败 |
| INVALID_TIMESTAMP | 时间戳过期 |
| SYSTEM_ERROR | 系统错误 |

## 权益卡状态说明
| 状态 | 说明 |
|------|------|
| PENDING | 待领取，还未到可领取时间 |
| ACTIVE | 有效，已领取且在有效期内 |
| EXPIRED | 已过期，超过30天有效期 |

## 业务规则
1. 每个订单生成6张权益卡
2. 第1张立即可领取，后续每隔5天可领取1张
3. 权益卡有效期为30天（从领取时间开始计算）
4. 用户可随时查询所有权益卡状态
5. 兑换码仅在权益卡被领取后生成

## 签名算法
使用HMAC-SHA256算法，签名字符串格式：
```
mobile=13534838490&orderId=YK202401010001&timestamp=1714463976291
```

参数按ASCII码升序排列，使用&连接，最后使用密钥进行HMAC-SHA256加密。
