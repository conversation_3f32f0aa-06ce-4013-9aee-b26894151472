# 充值通知接口文档

## 接口概述
零世纪系统主动调用优酷提供的订单状态查询接口，获取用户充值订单的详细信息。此接口基于优酷开放接口规范。

## 接口信息
- **接口名称**: 三方订单状态查询
- **请求方式**: POST
- **Content-Type**: application/json
- **接口地址**: `https://3part.youku.com/common/proxy/v1`

## 请求参数

### 请求头 (Header)
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Content-Type | String | 是 | application/json |
| yk23partappkey | String | 是 | 合作方渠道标示 |
| yk23partsign | String | 是 | 签名，使用HMAC_MD5算法 |
| timestamp | Long | 是 | 毫秒时间戳，有30分钟有效期 |

### 请求体 (Body)
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| apiName | String | 是 | 接口标识，固定值: `youku.union.order.query` |
| biz | String | 是 | 渠道订单标识 |
| orderId | String | 是 | 优酷订单ID |

### 请求示例
```json
{
  "apiName": "youku.union.order.query",
  "biz": "lingshiji_youku_2024",
  "orderId": "YK202401010001"
}
```

## 返回参数

### 公共字段
| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 开放接口请求是否成功 |
| msgCode | String | 开放接口状态码 |
| msgInfo | String | 开放接口状态码描述 |
| content | Object | 业务返回结果 |

### 业务字段 (content)
| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 业务是否成功 |
| code | String | 业务状态码 |
| msg | String | 业务状态码描述 |
| data | Object | 业务数据 |

### 订单数据 (data)
| 参数名 | 类型 | 说明 |
|--------|------|------|
| orderId | String | 优酷外部订单号 |
| isRefund | Boolean | 是否退款 |
| refundTime | Date | 退款时间 |
| cycleBuyType | String | 连续包订单类型 sign-签约,renew-续费 |
| unsignedState | String | 解约/冻结状态 UNSIGNED-解约, SIGNED_NO_OP-冻结 |
| unsignedTime | Date | 解约/冻结状态更新时间 |

### 成功响应示例
```json
{
  "success": true,
  "msgCode": "SUCCESS",
  "msgInfo": null,
  "content": {
    "success": true,
    "code": null,
    "msg": null,
    "data": {
      "orderId": "YK202401010001",
      "isRefund": false,
      "refundTime": null,
      "cycleBuyType": "sign",
      "unsignedState": null,
      "unsignedTime": null
    }
  }
}
```

### 退款订单响应示例
```json
{
  "success": true,
  "msgCode": "SUCCESS", 
  "msgInfo": null,
  "content": {
    "success": true,
    "code": null,
    "msg": null,
    "data": {
      "orderId": "YK202401010001",
      "isRefund": true,
      "refundTime": 1736826261000,
      "cycleBuyType": "renew",
      "unsignedState": "UNSIGNED",
      "unsignedTime": 1736826261000
    }
  }
}
```

## 零世纪业务处理逻辑

### 查询时机
1. 用户完成支付后，延迟5分钟开始查询
2. 查询间隔：1分钟、3分钟、5分钟、10分钟、30分钟
3. 最大查询次数：10次
4. 查询到成功状态后停止

### 业务状态判断
| 订单状态 | 业务处理 |
|----------|----------|
| isRefund=false | 订单支付成功，开始生成权益卡 |
| isRefund=true | 订单已退款，停止权益卡发放 |
| unsignedState=UNSIGNED | 用户解约，停止后续权益卡发放 |
| unsignedState=SIGNED_NO_OP | 订单冻结，暂停权益卡发放 |

### 权益卡生成规则
根据cycleBuyType判断用户类型：
- **sign（签约）**: 首次购买用户
  - 新用户1: 生成5元权益卡
  - 新用户2: 生成5元权益卡
- **renew（续费）**: 续费用户
  - 生成30元权益卡

## 状态码说明

### 开放接口状态码
| 状态码 | 说明 |
|--------|------|
| SUCCESS | 成功 |
| FAIL | 失败 |

### 业务状态码
| 状态码 | 说明 |
|--------|------|
| SUCCESS | 成功 |
| BIZ_ERROR | 业务异常 |
| FAIL_SERVICE_EXCEPTION | 服务未知异常 |
| APPTYPE_NOT_MATCH | 订单渠道校验失败 |
| FAIL_NO_PERMISSION | 未授权 |

## 签名算法
使用HMAC_MD5算法，签名字符串按ASCII码升序排列：
```
apiName=youku.union.order.query&biz=lingshiji_youku_2024&orderId=YK202401010001&timestamp=1714463976291
```

## 错误处理
1. 网络超时：重试机制
2. 签名错误：检查密钥和算法
3. 订单不存在：停止查询，记录异常
4. 权限不足：联系优酷技术支持
