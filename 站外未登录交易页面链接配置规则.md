# 站外未登录交易页面链接配置规则

## 链接上拼接手机号

### 步骤

1. **生成参数对象**：`{"mobile": "13534838490"}`

2. **将参数对象转换成 json 字符串并编码**得到：`%7B%22mobile%22%3A%2213534838490%22%7D`

3. **将第2步得到的字符串拼在h5链接上**，参数名为 `externalParams`

### 示例

- **h5源链接**：
  ```
  https://t.youku.com/yep/page/m/jbd477rggr?isNeedBaseImage=1
  ```

- **拼接手机号参数后链接**：
  ```
  https://t.youku.com/yep/page/m/jbd477rggr?isNeedBaseImage=1&externalParams=%7B%22mobile%22%3A%2213534838490%22%7D
  ```