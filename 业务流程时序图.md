# 优酷零世纪业务流程时序图

## 整体业务流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant LSJ_H5 as 零世纪H5页面
    participant LSJ_API as 零世纪API
    participant YK_Pay as 优酷收银台
    participant YK_API as 优酷API
    participant YK_App as 优酷App
    participant TM as 天猫系统

    Note over User, TM: 用户下单购买流程

    User->>LSJ_H5: 1. 访问广告宣传页
    User->>LSJ_H5: 2. 输入手机号点击立即领取

    LSJ_H5->>LSJ_API: 3. 调用订购号码校验接口
    Note right of LSJ_API: 校验手机号是否已参与活动
    LSJ_API-->>LSJ_H5: 4. 返回校验结果

    alt 校验通过
        LSJ_H5->>YK_Pay: 5. 跳转优酷收银台(拼接手机号)
        Note right of YK_Pay: 优酷判断新老用户展示不同价格

        alt 新用户1
            User->>YK_Pay: 6a. 支付1.99元
            YK_Pay-->>User: 7a. 优酷3天卡到账
            Note over LSJ_API, YK_API: 零世纪查询首次订单
            LSJ_API->>YK_API: 8a. 查询订单状态(sign)
            YK_API-->>LSJ_API: 9a. 返回签约订单信息
            LSJ_API->>LSJ_API: 10a. 生成5元天猫权益卡

            Note over YK_Pay, User: 3天后自动续费
            YK_Pay->>User: 11a. 自动扣费29.9元
            YK_Pay-->>User: 12a. 优酷月卡到账
            LSJ_API->>YK_API: 13a. 查询续费订单(renew)
            YK_API-->>LSJ_API: 14a. 返回续费订单信息
            LSJ_API->>LSJ_API: 15a. 生成30元天猫权益卡

        else 新用户2
            User->>YK_Pay: 6b. 支付2.99元
            YK_Pay-->>User: 7b. 优酷3天卡到账
            Note over LSJ_API, YK_API: 零世纪查询首次订单
            LSJ_API->>YK_API: 8b. 查询订单状态(sign)
            YK_API-->>LSJ_API: 9b. 返回签约订单信息
            LSJ_API->>LSJ_API: 10b. 生成5元天猫权益卡

            Note over YK_Pay, User: 3天后自动续费
            YK_Pay->>User: 11b. 自动扣费29.9元
            YK_Pay-->>User: 12b. 优酷月卡到账
            LSJ_API->>YK_API: 13b. 查询续费订单(renew)
            YK_API-->>LSJ_API: 14b. 返回续费订单信息
            LSJ_API->>LSJ_API: 15b. 生成30元天猫权益卡

        else 老用户
            User->>YK_Pay: 6c. 支付29.9元
            YK_Pay-->>User: 7c. 优酷月卡到账
            Note over LSJ_API, YK_API: 零世纪查询订单
            LSJ_API->>YK_API: 8c. 查询订单状态(sign)
            YK_API-->>LSJ_API: 9c. 返回签约订单信息
            LSJ_API->>LSJ_API: 10c. 生成30元天猫权益卡
        end

        Note over User, YK_App: 用户权益领取流程
        User->>YK_App: 16. 进入优酷App券包
        YK_App->>LSJ_API: 17. 点击权益领取链接(跳转零世纪页面)
        LSJ_API-->>User: 18. 展示权益领取页面
        User->>LSJ_API: 19. 点击领取权益卡
        LSJ_API-->>User: 20. 领取成功，显示权益卡信息

        Note over User, LSJ_API: 权益卡使用流程
        User->>LSJ_API: 21. 查看权益卡状态
        LSJ_API-->>User: 22. 返回权益卡信息和兑换码

    else 校验失败
        LSJ_API-->>LSJ_H5: 校验失败，显示错误信息
        LSJ_H5-->>User: 提示已参与过活动
    end
```

## 新老用户权益卡生成流程图

```mermaid
sequenceDiagram
    participant LSJ as 零世纪系统
    participant YK as 优酷系统
    participant User as 用户

    Note over LSJ, YK: 不同用户类型的权益卡生成规则

    rect rgb(240, 248, 255)
        Note over LSJ, YK: 新用户1流程 (1.99元)
        YK->>LSJ: 1. 首次订单支付成功(sign, 1.99元)
        LSJ->>LSJ: 2. 生成5元权益卡(6张)
        LSJ->>LSJ: 3. 设置第1张立即可领取

        Note over YK, User: 3天后自动续费
        YK->>LSJ: 4. 续费订单成功(renew, 29.9元)
        LSJ->>LSJ: 5. 生成30元权益卡(6张)
        LSJ->>LSJ: 6. 设置第1张立即可领取

        Note over YK, User: 每月自动续费
        loop 每月续费
            YK->>LSJ: 7. 月度续费(renew, 29.9元)
            LSJ->>LSJ: 8. 生成30元权益卡(6张)
        end
    end

    rect rgb(255, 248, 240)
        Note over LSJ, YK: 新用户2流程 (2.99元)
        YK->>LSJ: 1. 首次订单支付成功(sign, 2.99元)
        LSJ->>LSJ: 2. 生成5元权益卡(6张)
        LSJ->>LSJ: 3. 设置第1张立即可领取

        Note over YK, User: 3天后自动续费
        YK->>LSJ: 4. 续费订单成功(renew, 29.9元)
        LSJ->>LSJ: 5. 生成30元权益卡(6张)
        LSJ->>LSJ: 6. 设置第1张立即可领取

        Note over YK, User: 每月自动续费
        loop 每月续费
            YK->>LSJ: 7. 月度续费(renew, 29.9元)
            LSJ->>LSJ: 8. 生成30元权益卡(6张)
        end
    end

    rect rgb(248, 255, 248)
        Note over LSJ, YK: 老用户流程 (29.9元)
        YK->>LSJ: 1. 首次订单支付成功(sign, 29.9元)
        LSJ->>LSJ: 2. 生成30元权益卡(6张)
        LSJ->>LSJ: 3. 设置第1张立即可领取

        Note over YK, User: 每月自动续费
        loop 每月续费
            YK->>LSJ: 4. 月度续费(renew, 29.9元)
            LSJ->>LSJ: 5. 生成30元权益卡(6张)
        end
    end
```

## 权益卡领取详细时序图

```mermaid
sequenceDiagram
    participant LSJ as 零世纪系统
    participant YK as 优酷系统
    participant User as 用户
    participant TM as 天猫系统

    Note over LSJ, TM: 权益卡领取时序详细流程

    rect rgb(240, 248, 255)
        Note over LSJ, YK: 第一阶段：订单完成后权益卡生成
        YK->>LSJ: 1. 订单支付成功通知
        alt 新用户首次(1.99/2.99元)
            LSJ->>LSJ: 2a. 生成5元权益卡(6张)
        else 续费或老用户(29.9元)
            LSJ->>LSJ: 2b. 生成30元权益卡(6张)
        end
        LSJ->>LSJ: 3. 设置第1张为可领取状态
    end

    rect rgb(255, 248, 240)
        Note over User, LSJ: 第二阶段：用户领取权益卡（每隔5天）
        loop 每隔5天领取1张，共6次
            User->>LSJ: 4. 访问权益领取页面
            LSJ->>LSJ: 5. 检查领取条件
            alt 可以领取
                LSJ->>LSJ: 6. 生成权益卡
                LSJ-->>User: 7. 返回权益卡信息
                LSJ->>LSJ: 8. 更新下次可领取时间
            else 不可领取
                LSJ-->>User: 9. 返回等待时间提示
            end
        end
    end

    rect rgb(248, 255, 248)
        Note over User, LSJ: 第三阶段：权益卡状态查询
        User->>LSJ: 10. 查询权益卡状态
        LSJ->>LSJ: 11. 检查权益卡有效性
        LSJ-->>User: 12. 返回权益卡信息和兑换码
        Note over User: 用户自行在天猫使用兑换码
    end
```

## 订购号码校验流程图

```mermaid
flowchart TD
    A[用户输入手机号] --> B[调用订购号码校验接口]
    B --> C{手机号格式正确?}
    C -->|否| D[返回格式错误]
    C -->|是| E{查询活动参与记录}
    E -->|已参与| F[返回已参与提示]
    E -->|未参与| G[返回可以下单]
    G --> H[跳转优酷收银台]
    D --> I[显示错误信息]
    F --> I
```

## 用户类型判断流程图

```mermaid
flowchart TD
    A[用户进入优酷收银台] --> B{优酷判断用户类型}
    B -->|新用户1| C[展示1.99元套餐]
    B -->|新用户2| D[展示2.99元套餐]
    B -->|老用户| E[展示29.9元套餐]

    C --> F[优酷3天卡 + 5元权益卡]
    D --> G[优酷3天卡 + 5元权益卡]
    E --> H[优酷月卡 + 30元权益卡]

    F --> I[3天后自动续费29.9元]
    G --> I
    I --> J[优酷月卡 + 30元权益卡]

    H --> K[每月自动续费29.9元]
    J --> K
    K --> L[优酷月卡 + 30元权益卡]
```

## 权益卡状态流转图

```mermaid
stateDiagram-v2
    [*] --> 待生成: 订单支付成功
    待生成 --> 可领取: 到达领取时间
    可领取 --> 已领取: 用户点击领取
    已领取 --> 已过期: 超过有效期
    已过期 --> [*]

    note right of 可领取: 每隔5天可领取1张
    note right of 已领取: 权益卡有效期30天，包含兑换码
    note right of 已过期: 用户自行在天猫使用兑换码

    state 待生成 {
        [*] --> 新用户首次: 1.99/2.99元
        [*] --> 续费老用户: 29.9元
        新用户首次 --> 生成5元卡
        续费老用户 --> 生成30元卡
    }
```

## 月度自动续费流程时序图

```mermaid
sequenceDiagram
    participant YK_System as 优酷系统
    participant YK_Pay as 优酷支付
    participant LSJ_API as 零世纪API
    participant User as 用户
    participant YK_App as 优酷App

    Note over YK_System, YK_App: 每月自动续费流程 (29.9元/月)

    rect rgb(255, 248, 240)
        Note over YK_System, LSJ_API: 自动续费扣款
        YK_System->>YK_Pay: 1. 触发月度自动扣费
        YK_Pay->>YK_Pay: 2. 从用户账户扣费29.9元
        YK_Pay-->>User: 3. 发送扣费通知
        YK_Pay->>YK_System: 4. 扣费成功，生成续费订单
        YK_System->>User: 5. 优酷月卡自动到账
    end

    rect rgb(240, 248, 255)
        Note over LSJ_API, YK_System: 零世纪查询续费订单
        LSJ_API->>YK_System: 6. 调用订单状态查询接口
        Note right of YK_System: cycleBuyType: renew
        YK_System-->>LSJ_API: 7. 返回续费订单信息
        LSJ_API->>LSJ_API: 8. 生成30元天猫权益卡(6张)
        LSJ_API->>LSJ_API: 9. 设置第1张立即可领取
    end

    rect rgb(248, 255, 248)
        Note over User, YK_App: 用户领取权益卡
        User->>YK_App: 10. 进入优酷App券包
        YK_App->>LSJ_API: 11. 点击权益领取链接
        LSJ_API-->>User: 12. 展示权益领取页面
        User->>LSJ_API: 13. 领取第1张30元权益卡

        Note over User, LSJ_API: 后续每隔5天领取1张
        loop 每隔5天，共6次
            User->>LSJ_API: 14. 访问权益领取页面
            LSJ_API-->>User: 15. 领取下一张30元权益卡
        end
    end

    Note over YK_System, YK_App: 下个月重复此流程
```

## 连续包月订单类型说明

```mermaid
graph LR
    A[用户支付] --> B{订单类型判断}
    B -->|首次购买| C[cycleBuyType: sign]
    B -->|自动续费| D[cycleBuyType: renew]

    C --> E{用户类型}
    E -->|新用户1/2| F[生成5元权益卡]
    E -->|老用户| G[生成30元权益卡]

    D --> H[生成30元权益卡]

    F --> I[6张权益卡，每隔5天领取1张]
    G --> I
    H --> I

    I --> J[下个月自动续费]
    J --> D
```

## 用户生命周期完整流程图

```mermaid
timeline
    title 用户完整生命周期

    section 新用户1
        首次购买 : 1.99元
                 : 优酷3天卡
                 : 5元权益卡×6张

        3天后续费 : 29.9元自动扣费
                  : 优酷月卡
                  : 30元权益卡×6张

        每月续费 : 29.9元自动扣费
                 : 优酷月卡
                 : 30元权益卡×6张

    section 新用户2
        首次购买 : 2.99元
                 : 优酷3天卡
                 : 5元权益卡×6张

        3天后续费 : 29.9元自动扣费
                  : 优酷月卡
                  : 30元权益卡×6张

        每月续费 : 29.9元自动扣费
                 : 优酷月卡
                 : 30元权益卡×6张

    section 老用户
        首次购买 : 29.9元
                 : 优酷月卡
                 : 30元权益卡×6张

        每月续费 : 29.9元自动扣费
                 : 优酷月卡
                 : 30元权益卡×6张
```
