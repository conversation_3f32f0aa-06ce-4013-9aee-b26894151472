# 订购号码校验接口文档

## 接口概述
用于校验手机号是否已经参与过活动，防止重复参与。

## 接口信息
- **接口名称**: 订购号码校验
- **请求方式**: POST
- **Content-Type**: application/json
- **接口地址**: `https://api.lingshiji.com/youku/mobile/check`

## 请求参数

### 请求头 (Header)
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Content-Type | String | 是 | application/json |
| Authorization | String | 是 | Bearer token认证 |
| timestamp | Long | 是 | 毫秒时间戳 |
| sign | String | 是 | 签名，使用HMAC-SHA256算法 |

### 请求体 (Body)
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mobile | String | 是 | 手机号码，11位数字 |
| activityId | String | 是 | 活动ID，标识具体活动 |
| source | String | 否 | 来源渠道，默认为"youku" |

### 请求示例
```json
{
  "mobile": "13534838490",
  "activityId": "youku_tmall_2024",
  "source": "youku"
}
```

## 返回参数

### 响应体结构
| 参数名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| code | String | 状态码 |
| message | String | 状态描述 |
| data | Object | 业务数据 |

### 业务数据 (data)
| 参数名 | 类型 | 说明 |
|--------|------|------|
| mobile | String | 手机号码 |
| canOrder | Boolean | 是否可以下单 |
| reason | String | 不能下单的原因（当canOrder为false时） |
| participateTime | Long | 首次参与时间戳（如果已参与） |

### 成功响应示例
```json
{
  "success": true,
  "code": "SUCCESS",
  "message": "校验成功",
  "data": {
    "mobile": "13534838490",
    "canOrder": true,
    "reason": null,
    "participateTime": null
  }
}
```

### 已参与活动响应示例
```json
{
  "success": true,
  "code": "SUCCESS", 
  "message": "校验成功",
  "data": {
    "mobile": "13534838490",
    "canOrder": false,
    "reason": "该手机号已参与过此活动",
    "participateTime": 1714463976291
  }
}
```

### 错误响应示例
```json
{
  "success": false,
  "code": "INVALID_MOBILE",
  "message": "手机号格式不正确",
  "data": null
}
```

## 状态码说明
| 状态码 | 说明 |
|--------|------|
| SUCCESS | 成功 |
| INVALID_MOBILE | 手机号格式不正确 |
| INVALID_ACTIVITY | 活动ID不存在 |
| SYSTEM_ERROR | 系统错误 |
| INVALID_SIGN | 签名验证失败 |
| INVALID_TIMESTAMP | 时间戳过期 |

## 签名算法
使用HMAC-SHA256算法，签名字符串格式：
```
mobile=13534838490&activityId=youku_tmall_2024&source=youku&timestamp=1714463976291
```

参数按ASCII码升序排列，使用&连接，最后使用密钥进行HMAC-SHA256加密。
